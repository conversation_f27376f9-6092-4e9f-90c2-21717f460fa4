[2025/07/30 11:11:56] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 11:11:56] [MainThread] [Debug] 手机号码获取成功: 8068559773
[2025/07/30 11:11:57] [MainThread] [Success] 代理检测通过: 127.0.0.1:50144, 0.872385s
[2025/07/30 11:11:57] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 11:11:57] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 11:12:02] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 11:12:02] [MainThread] [Notice] wait for selector: button:has-text("No, Thank You!")
[2025/07/30 11:12:23] [MainThread] [Notice] COUNT = 0
[2025/07/30 11:18:20] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 11:18:20] [MainThread] [Debug] 手机号码获取成功: 8323242450
[2025/07/30 11:18:23] [MainThread] [Success] 代理检测通过: 127.0.0.1:50163, 2.804554s
[2025/07/30 11:18:24] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 11:18:24] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-25-under?id=305359&edge=hybrid
[2025/07/30 11:18:28] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 11:18:28] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 11:18:39] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 11:18:41] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 11:18:50] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 11:18:52] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 11:18:54] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery371024858263320089125_1753845513729&campaign_id=2842942&date_formatted=7%2F29%2F2025%2020%3A18%3A52.341&email=john1970dmurn5%40emailmacys.store&carb-trap=&email=john1970dmurn5%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753845514428&useragent:devicetype=desktop&step=2&visit_id=1753845514436175&m=0&d=d&cookie=%7B%22did%22%3A%228593214334872135393%22%2C%22vid%22%3A1753845514436175%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753845514428%7D%2C%22fvt%22%3A1753845514%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-25-under%253Fid%253D305359%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753845514%2C%22sid%22%3A12%2C%22gcr%22%3A95%2C%22m%22%3A0%2C%22lvt%22%3A1753845517%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753845517%2C%22lavid%22%3A1753845514436175%2C%22la%22%3A1753845516%2C%22av%22%3A1%2C%22fsa%22%3A1753845516%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753845516%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753845517%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753845517%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753845517%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=8593214334872135393&cts=1753845532344&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-25-under%3Fid%3D305359%26edge%3Dhybrid&request_token=8144ff2b4e16c24cc7f28d108a08c39de6cba44cfa93b7ad61ba9f5b76e6a061&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T03%3A18%3A28.688Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753845518371%2C%22eift%22%3A1753845519730%2C%22eifet%22%3A3%2C%22eibt%22%3A1753845530260%2C%22eibet%22%3A3%7D&_=1753845513730
[2025/07/30 11:18:54] [MainThread] [Debug] jQuery371024858263320089125_1753845513729()
[2025/07/30 11:18:56] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 11:19:01] [MainThread] [Notice] 手机号码已填写: 8323246378
[2025/07/30 11:19:03] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 11:19:06] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery371024858263320089125_1753845513729&campaign_id=2846223&date_formatted=7%2F29%2F2025%2020%3A19%3A04.188&phone_number=8323247850&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753845534295&useragent:devicetype=desktop&step=1&visit_id=1753845514436175&m=0&d=d&cookie=%7B%22did%22%3A%228593214334872135393%22%2C%22vid%22%3A1753845514436175%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753845534295%7D%2C%22fvt%22%3A1753845514%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-25-under%253Fid%253D305359%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753845514%2C%22sid%22%3A15%2C%22gcr%22%3A95%2C%22m%22%3A0%2C%22lvt%22%3A1753845532%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753845517%2C%22lavid%22%3A1753845514436175%2C%22la%22%3A1753845516%2C%22av%22%3A1%2C%22fsa%22%3A1753845516%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753845516%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753845517%2C%22ls%22%3A1753845532%2C%22wcv%22%3A1753845532%2C%22wc%22%3A1753845532%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753845532%2C%22lavid%22%3A1753845514436175%2C%22la%22%3A1753845532%2C%22av%22%3A1%2C%22fsa%22%3A1753845532%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753845517%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=8593214334872135393&cts=1753845544189&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-25-under%3Fid%3D305359%26edge%3Dhybrid&request_token=8144ff2b4e16c24cc7f28d108a08c39de6cba44cfa93b7ad61ba9f5b76e6a061&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T03%3A18%3A28.688Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753845534685%7D&_=1753845513731
[2025/07/30 11:19:06] [MainThread] [Debug] jQuery371024858263320089125_1753845513729()
[2025/07/30 11:19:08] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 11:19:09] [MainThread] [Notice] _exit = 5
[2025/07/30 11:19:09] [MainThread] [Debug] https://www.macys.com/shop/product/zwilling-j.a.-henckels-pro-solid-white-16-slot-block?ID=8999951
[2025/07/30 11:19:14] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 11:19:14] [MainThread] [Notice] clicked: button:has-text("Add To Bag")
[2025/07/30 11:19:15] [MainThread] [Success] https://www.macys.com/xapi/bag/v1/add, {"item":{"upcId":"41477917","upcNumber":"35886399597","registryId":0,"source":"PDPA2B","loyaltyPoints":true,"quantity":1,"pickUpFromStore":"false","pdpAtbProtectionPlanEligible":false,"isMarketPlaceEligibleFlag":false,"sellerId":null,"autoreplenishment":null}}
[2025/07/30 11:19:15] [MainThread] [Error] Response.json: Response body is unavailable for redirect responses
[2025/07/30 11:19:15] [MainThread] [Debug] https://www.macys.com/shop/product/kramer-by-zwilling-j.a.-henckels-5-utility-knife?ID=9000016
[2025/07/30 11:19:19] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 11:19:20] [MainThread] [Notice] clicked: button:has-text("Add To Bag")
[2025/07/30 11:19:20] [MainThread] [Success] https://www.macys.com/xapi/bag/v1/add, {"item":{"upcId":"41165395","upcNumber":"35886362287","registryId":0,"source":"PDPA2B","loyaltyPoints":true,"quantity":1,"pickUpFromStore":"false","pdpAtbProtectionPlanEligible":false,"isMarketPlaceEligibleFlag":false,"sellerId":null,"autoreplenishment":null}}
[2025/07/30 11:19:20] [MainThread] [Error] Response.json: Response body is unavailable for redirect responses
[2025/07/30 11:19:20] [MainThread] [Debug] https://www.macys.com/shop/product/zwilling-j.a.-henckels-twin-signature-5-serrated-utility-knife?ID=4029428
[2025/07/30 11:19:21] [MainThread] [Notice] COUNT = 0
[2025/07/30 11:55:00] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 11:55:00] [MainThread] [Debug] 手机号码获取成功: 9289519376
[2025/07/30 11:55:01] [MainThread] [Success] 代理检测通过: 127.0.0.1:50076, 0.171974s
[2025/07/30 11:55:01] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 11:55:01] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-50-under/Pageindex/6?id=305357
[2025/07/30 11:55:05] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 11:55:05] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 11:55:13] [MainThread] [Error] 鼠标移动失败: sleep length must be non-negative
[2025/07/30 11:55:14] [MainThread] [Debug] 鼠标点击: (640, 360)
[2025/07/30 11:55:14] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 11:55:16] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Click to Claim 25% Off'}
[2025/07/30 11:55:18] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Decline Offer'}
[2025/07/30 11:55:20] [MainThread] [Debug] 焦点元素信息: {'tagName': 'A', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': None, 'text': '*Offer exclusions apply'}
[2025/07/30 11:55:22] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': 'bx-close-inside-2842942', 'className': 'bx-close bx-close-link bx-close-inside', 'placeholder': None, 'value': '', 'text': 'close email sign up dialog'}
[2025/07/30 11:55:24] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Click to Claim 25% Off'}
[2025/07/30 11:55:26] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Decline Offer'}
[2025/07/30 11:55:28] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:48:29] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 12:48:29] [MainThread] [Debug] 手机号码获取成功: 3023684836
[2025/07/30 12:48:29] [MainThread] [Success] 代理检测通过: 127.0.0.1:50199, 0.390946s
[2025/07/30 12:48:30] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:48:30] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-25-under?id=305359&edge=hybrid
[2025/07/30 12:48:36] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 12:48:36] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 12:48:41] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 12:48:46] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 12:48:46] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQq+1l8n7crufpSadgzmmCqeQQBs7t7arjHaxF5plGwcwHbAek6U2MAAjgCuiOFOugxC0PSO8NhI0ipunt6+cWSulNW1yEwYNlAmIgi4TRzuIdnQ4biYlEgAFtCQmAD6bJBIAJ7rjSKQTFnrmPZMhkLAkQBmTEKFiwBeIrgAtKm6mcDruxC4zDMHEOOwKIhKkDmBkMlGO9FwJUKkCAA
[2025/07/30 12:48:47] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 12:48:49] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 12:48:49] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 12:48:49] [MainThread] [Debug] 开始安全点击元素: button:has-text("Click to Claim 25% Off")
[2025/07/30 12:48:49] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 12:48:49] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Click to Claim 25% Off")
[2025/07/30 12:48:49] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 12:49:24] [MainThread] [Debug] 鼠标移动到: (639, 441)
[2025/07/30 12:49:58] [MainThread] [Debug] 鼠标移动到: (639, 441)
[2025/07/30 12:50:00] [MainThread] [Debug] 鼠标点击: (639, 441)
[2025/07/30 12:50:00] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Click to Claim 25% Off")
[2025/07/30 12:50:00] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 12:50:03] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 12:50:31] [MainThread] [Debug] 输入文本: <EMAIL>
[2025/07/30 12:50:31] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 12:50:33] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 12:50:38] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37108058358033558076_1753850923182&campaign_id=2842942&date_formatted=7%2F29%2F2025%2021%3A50%3A33.899&email=linda19703lvus4%40emailmacys.store&carb-trap=&email=linda19703lvus4%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753850923794&useragent:devicetype=desktop&step=2&visit_id=1753850923801323&m=0&d=d&cookie=%7B%22did%22%3A%221383263500560485858%22%2C%22vid%22%3A1753850923801323%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753850923794%7D%2C%22fvt%22%3A1753850923%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-25-under%253Fid%253D305359%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753850923%2C%22sid%22%3A15%2C%22gcr%22%3A42%2C%22m%22%3A0%2C%22lvt%22%3A1753850926%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753850926%2C%22lavid%22%3A1753850923801323%2C%22la%22%3A1753850925%2C%22av%22%3A1%2C%22fsa%22%3A1753850925%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753850925%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753850998%7D%2C%222846223%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753850926%7D%2C%222846225%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753850926%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=1383263500560485858&cts=1753851033901&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-25-under%3Fid%3D305359%26edge%3Dhybrid&request_token=3c627a772e939b6bc6062a72a3d368cac4c8e92f5c0cc3cbb17b011cf972333d&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T04%3A48%3A38.332Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753850926320%2C%22eift%22%3A1753851001461%2C%22eifet%22%3A3%2C%22eibt%22%3A1753851031799%2C%22eibet%22%3A3%7D&_=1753850923183
[2025/07/30 12:50:38] [MainThread] [Debug] jQuery37108058358033558076_1753850923182()
[2025/07/30 12:50:40] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 12:50:49] [MainThread] [Debug] 输入文本: 3023683730
[2025/07/30 12:50:49] [MainThread] [Notice] 手机号码已填写: 3023683730
[2025/07/30 12:50:51] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 12:50:54] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37108058358033558076_1753850923182&campaign_id=2846223&date_formatted=7%2F29%2F2025%2021%3A50%3A52.729&phone_number=3023684288&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753851034590&useragent:devicetype=desktop&step=1&visit_id=1753850923801323&m=0&d=d&cookie=%7B%22did%22%3A%221383263500560485858%22%2C%22vid%22%3A1753850923801323%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753851034590%7D%2C%22fvt%22%3A1753850923%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-25-under%253Fid%253D305359%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753850923%2C%22sid%22%3A18%2C%22gcr%22%3A42%2C%22m%22%3A0%2C%22lvt%22%3A1753851037%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753850926%2C%22lavid%22%3A1753850923801323%2C%22la%22%3A1753850925%2C%22av%22%3A1%2C%22fsa%22%3A1753850925%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753850925%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753850998%2C%22ls%22%3A1753851037%2C%22wcv%22%3A1753851037%2C%22wc%22%3A1753851037%7D%2C%222846223%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753851037%2C%22lavid%22%3A1753850923801323%2C%22la%22%3A1753851037%2C%22av%22%3A1%2C%22fsa%22%3A1753851037%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753850926%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=1383263500560485858&cts=1753851052730&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-25-under%3Fid%3D305359%26edge%3Dhybrid&request_token=3c627a772e939b6bc6062a72a3d368cac4c8e92f5c0cc3cbb17b011cf972333d&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T04%3A48%3A38.332Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753851038536%7D&_=1753850923184
[2025/07/30 12:50:54] [MainThread] [Debug] jQuery37108058358033558076_1753850923182()
[2025/07/30 12:50:56] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 12:50:57] [MainThread] [Notice] _exit = 5
[2025/07/30 12:50:57] [MainThread] [Debug] https://www.macys.com/shop/product/laguiole-evolution-7-in-1-kitchen-scissors?ID=10210360
[2025/07/30 12:51:01] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 12:51:01] [MainThread] [Debug] 开始安全点击元素: button:has-text("Add To Bag")
[2025/07/30 12:51:01] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 12:51:01] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Add To Bag")
[2025/07/30 12:51:01] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 12:51:37] [MainThread] [Debug] 鼠标移动到: (997, 411)
[2025/07/30 12:52:11] [MainThread] [Debug] 鼠标移动到: (997, 411)
[2025/07/30 12:52:13] [MainThread] [Debug] 鼠标点击: (997, 411)
[2025/07/30 12:52:13] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Add To Bag")
[2025/07/30 12:52:13] [MainThread] [Notice] clicked Add To Bag: button:has-text("Add To Bag")
[2025/07/30 12:52:13] [MainThread] [Notice] 点击了Add To Bag按钮，但未监听到返回: Timeout 10000ms exceeded while waiting for event "response"
=========================== logs ===========================
waiting for response https://www.macys.com/xapi/bag/v1/add
============================================================
[2025/07/30 12:52:13] [MainThread] [Notice] 已点击Add To Bag按钮，结束add_cart函数
[2025/07/30 12:52:13] [MainThread] [Debug] 当前购物车数量: 0
[2025/07/30 12:52:13] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:52:14] [MainThread] [Notice] cleanup.
[2025/07/30 12:52:14] [MainThread] [Notice] exit.
[2025/07/30 13:05:49] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 13:05:49] [MainThread] [Debug] 手机号码获取成功: 7193981834
[2025/07/30 13:05:50] [MainThread] [Success] 代理检测通过: 127.0.0.1:50161, 0.373225s
[2025/07/30 13:05:50] [MainThread] [Debug] GhostCursor启用快速模式
[2025/07/30 13:05:50] [MainThread] [Debug] GhostCursor快速模式: 启用
[2025/07/30 13:05:50] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 13:05:50] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-10-under?id=305358
[2025/07/30 13:05:54] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 13:05:54] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 13:05:59] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 13:06:04] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 13:06:05] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQq+VkAbNtdXMkJ8YkpNOwZzTFwQ4mVlQm1td19ld3dyNxJKNg5gO2A9J0psYABHAFdEKJ5nSgYhaHpHeGwkaRU3T28-V21CQgam+iYMGygTEQRcDo53cILoKNxMSiQAC2hITAB9NkgkAE9d9pFIJnzdzHsmQyFgGIAzJiEy9YAvEVwAWmIk3KXYC7Y4QXDMMwcc5HUoiSqQFYGQyUIHIXCVMqQIA
[2025/07/30 13:06:05] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 13:06:07] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:06:07] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:06:07] [MainThread] [Debug] 开始安全点击元素: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:06:07] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 13:06:07] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Click to Claim 25% Off")
[2025/07/30 13:06:07] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 13:06:15] [MainThread] [Debug] 鼠标移动到: (638, 438)
[2025/07/30 13:06:23] [MainThread] [Debug] 鼠标移动到: (638, 438)
[2025/07/30 13:06:24] [MainThread] [Debug] 鼠标点击: (638, 438)
[2025/07/30 13:06:24] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:06:24] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:06:27] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 13:06:53] [MainThread] [Debug] 输入文本: <EMAIL>
[2025/07/30 13:06:53] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 13:06:55] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 13:06:59] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37107563345156508291_1753851959295&campaign_id=2842942&date_formatted=7%2F29%2F2025%2022%3A06%3A55.454&email=luis1973idc2ey%40emailmacys.store&carb-trap=&email=luis1973idc2ey%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753851960554&useragent:devicetype=desktop&step=2&visit_id=1753851960559824&m=0&d=d&cookie=%7B%22did%22%3A%228241180036133297584%22%2C%22vid%22%3A1753851960559824%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753851960554%7D%2C%22fvt%22%3A1753851960%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-10-under%253Fid%253D305358%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753851960%2C%22sid%22%3A12%2C%22gcr%22%3A48%2C%22m%22%3A0%2C%22lvt%22%3A1753851964%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753851964%2C%22lavid%22%3A1753851960559824%2C%22la%22%3A1753851963%2C%22av%22%3A1%2C%22fsa%22%3A1753851963%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753851963%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753851982%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753851964%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753851964%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=8241180036133297584&cts=1753852015458&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-10-under%3Fid%3D305358&request_token=d224eaa70270a40269035695b9c508dc6878a98fdb0149612f60e6b1dbc97643&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T05%3A05%3A54.664Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753851965019%2C%22eift%22%3A1753851985853%2C%22eifet%22%3A3%2C%22eibt%22%3A1753852013370%2C%22eibet%22%3A3%7D&_=1753851959296
[2025/07/30 13:06:59] [MainThread] [Debug] jQuery37107563345156508291_1753851959295()
[2025/07/30 13:07:01] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 13:07:10] [MainThread] [Debug] 输入文本: 7193985740
[2025/07/30 13:07:10] [MainThread] [Notice] 手机号码已填写: 7193985740
[2025/07/30 13:07:12] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 13:07:14] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37107563345156508291_1753851959295&campaign_id=2846223&date_formatted=7%2F29%2F2025%2022%3A07%3A13.201&phone_number=7193989456&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753852017127&useragent:devicetype=desktop&step=1&visit_id=1753851960559824&m=0&d=d&cookie=%7B%22did%22%3A%228241180036133297584%22%2C%22vid%22%3A1753851960559824%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753852017127%7D%2C%22fvt%22%3A1753851960%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-10-under%253Fid%253D305358%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753851960%2C%22sid%22%3A15%2C%22gcr%22%3A48%2C%22m%22%3A0%2C%22lvt%22%3A1753852017%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753851964%2C%22lavid%22%3A1753851960559824%2C%22la%22%3A1753851963%2C%22av%22%3A1%2C%22fsa%22%3A1753851963%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753851963%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753851982%2C%22ls%22%3A1753852017%2C%22wcv%22%3A1753852017%2C%22wc%22%3A1753852017%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753852017%2C%22lavid%22%3A1753851960559824%2C%22la%22%3A1753852017%2C%22av%22%3A1%2C%22fsa%22%3A1753852017%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753851964%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=8241180036133297584&cts=1753852033202&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-10-under%3Fid%3D305358&request_token=d224eaa70270a40269035695b9c508dc6878a98fdb0149612f60e6b1dbc97643&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T05%3A05%3A54.664Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753852019487%7D&_=1753851959297
[2025/07/30 13:07:14] [MainThread] [Debug] jQuery37107563345156508291_1753851959295()
[2025/07/30 13:07:16] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 13:07:17] [MainThread] [Notice] _exit = 5
[2025/07/30 13:07:17] [MainThread] [Debug] https://www.macys.com/shop/product/kramer-by-zwilling-j.a.-henckels-euroline-damascus-collection-9-carving-knife?ID=8999909
[2025/07/30 13:07:20] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 13:07:20] [MainThread] [Debug] 开始安全点击元素: button:has-text("Add To Bag")
[2025/07/30 13:07:20] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 13:07:20] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Add To Bag")
[2025/07/30 13:07:20] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 13:07:28] [MainThread] [Debug] 鼠标移动到: (998, 440)
[2025/07/30 13:07:36] [MainThread] [Debug] 鼠标移动到: (998, 440)
[2025/07/30 13:07:37] [MainThread] [Debug] 鼠标点击: (998, 440)
[2025/07/30 13:07:37] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Add To Bag")
[2025/07/30 13:07:37] [MainThread] [Notice] clicked Add To Bag: button:has-text("Add To Bag")
[2025/07/30 13:07:40] [MainThread] [Notice] 点击了Add To Bag按钮，但未监听到返回: Timeout 20000ms exceeded while waiting for event "response"
=========================== logs ===========================
waiting for response https://www.macys.com/xapi/bag/v1/add
============================================================
[2025/07/30 13:07:40] [MainThread] [Notice] 已点击Add To Bag按钮，结束add_cart函数
[2025/07/30 13:07:40] [MainThread] [Debug] 当前购物车数量: 0
[2025/07/30 13:07:40] [MainThread] [Notice] COUNT = 0
[2025/07/30 13:07:40] [MainThread] [Notice] cleanup.
[2025/07/30 13:07:40] [MainThread] [Notice] exit.
[2025/07/30 13:12:48] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 13:12:48] [MainThread] [Debug] 手机号码获取成功: 5203167837
[2025/07/30 13:12:50] [MainThread] [Error] 代理检测未通过: 127.0.0.1:50359
[2025/07/30 13:12:54] [MainThread] [Success] 代理检测通过: 127.0.0.1:50234, 4.502444s
[2025/07/30 13:12:54] [MainThread] [Debug] GhostCursor启用快速模式
[2025/07/30 13:12:54] [MainThread] [Debug] GhostCursor快速模式: 启用
[2025/07/30 13:12:54] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 13:12:54] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-25-under?id=305359&edge=hybrid
[2025/07/30 13:13:00] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 13:13:00] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 13:13:06] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 13:13:11] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 13:13:16] [MainThread] [Debug] 已等待 15 秒，继续等待弹窗信号...
[2025/07/30 13:13:21] [MainThread] [Debug] 已等待 20 秒，继续等待弹窗信号...
[2025/07/30 13:13:26] [MainThread] [Debug] 已等待 25 秒，继续等待弹窗信号...
[2025/07/30 13:13:31] [MainThread] [Debug] 已等待 30 秒，继续等待弹窗信号...
[2025/07/30 13:13:31] [MainThread] [Error] 等待 30 秒后仍未检测到弹窗信号
[2025/07/30 13:13:31] [MainThread] [Notice] COUNT = 0
[2025/07/30 13:13:31] [MainThread] [Notice] cleanup.
[2025/07/30 13:13:31] [MainThread] [Notice] exit.
[2025/07/30 13:14:08] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 13:14:08] [MainThread] [Debug] 手机号码获取成功: 4809869306
[2025/07/30 13:14:09] [MainThread] [Success] 代理检测通过: 127.0.0.1:50104, 0.585901s
[2025/07/30 13:14:09] [MainThread] [Debug] GhostCursor启用快速模式
[2025/07/30 13:14:09] [MainThread] [Debug] GhostCursor快速模式: 启用
[2025/07/30 13:14:09] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 13:14:09] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-50-under/Pageindex/6?id=305357
[2025/07/30 13:14:14] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 13:14:14] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 13:14:19] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 13:14:21] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQq-zFXhN8SE2s6UmnYM5pi4hABs+M6B2mSuFGQxZITK3jGUbBzAdsB6TpTYwACOAK6IEU66DELQ9I7w2EjSKm6e3sQxyu4DlA1NyEwYNlAmIgi47RzuoQXQEbiYlEgAFtCQmAD6bJBIAJ67bSKQTPm7mPZMhkLAUQBmTEJl6wBeIrgAtIm5l2Au2OEFwzDMHHOR1KIkqkBWBkMlEByFwlTKkCAA
[2025/07/30 13:14:22] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 13:14:24] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:14:24] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:14:24] [MainThread] [Debug] 开始安全点击元素: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:14:24] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 13:14:24] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Click to Claim 25% Off")
[2025/07/30 13:14:24] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 13:14:32] [MainThread] [Debug] 鼠标移动到: (642, 438)
[2025/07/30 13:14:34] [MainThread] [Debug] 鼠标点击: (642, 438)
[2025/07/30 13:14:34] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:14:34] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:14:37] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 13:15:07] [MainThread] [Debug] 输入文本: <EMAIL>
[2025/07/30 13:15:07] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 13:15:09] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 13:15:12] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery3710769422935858829_1753852457827&campaign_id=2842942&date_formatted=7%2F29%2F2025%2022%3A15%3A09.332&email=kimberly19350x43i7%40emailmacys.store&carb-trap=&email=kimberly19350x43i7%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753852458747&useragent:devicetype=desktop&step=2&visit_id=1753852458754807&m=0&d=d&cookie=%7B%22did%22%3A%228627480959496981526%22%2C%22vid%22%3A1753852458754807%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753852458747%7D%2C%22fvt%22%3A1753852458%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-50-under%252FPageindex%252F6%253Fid%253D305357%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753852458%2C%22sid%22%3A15%2C%22gcr%22%3A14%2C%22m%22%3A0%2C%22lvt%22%3A1753852462%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753852462%2C%22lavid%22%3A1753852458754807%2C%22la%22%3A1753852460%2C%22av%22%3A1%2C%22fsa%22%3A1753852460%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753852460%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753852472%7D%2C%222846223%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753852462%7D%2C%222846225%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753852462%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=8627480959496981526&cts=1753852509335&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-50-under%2FPageindex%2F6%3Fid%3D305357&request_token=e0864cc6d8e9b0809c557f057c6bc87e9d19d705e213762c70d5e71349d961b2&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T05%3A14%3A14.820Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753852461264%2C%22eift%22%3A1753852475480%2C%22eifet%22%3A3%2C%22eibt%22%3A1753852507288%2C%22eibet%22%3A3%7D&_=1753852457828
[2025/07/30 13:15:12] [MainThread] [Debug] jQuery3710769422935858829_1753852457827()
[2025/07/30 13:15:14] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 13:15:22] [MainThread] [Debug] 输入文本: 4809861509
[2025/07/30 13:15:22] [MainThread] [Notice] 手机号码已填写: 4809861509
[2025/07/30 13:15:24] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 13:15:26] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery3710769422935858829_1753852457827&campaign_id=2846223&date_formatted=7%2F29%2F2025%2022%3A15%3A25.700&phone_number=4809868109&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753852509837&useragent:devicetype=desktop&step=1&visit_id=1753852458754807&m=0&d=d&cookie=%7B%22did%22%3A%228627480959496981526%22%2C%22vid%22%3A1753852458754807%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753852509837%7D%2C%22fvt%22%3A1753852458%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-50-under%252FPageindex%252F6%253Fid%253D305357%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753852458%2C%22sid%22%3A18%2C%22gcr%22%3A14%2C%22m%22%3A0%2C%22lvt%22%3A1753852511%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753852462%2C%22lavid%22%3A1753852458754807%2C%22la%22%3A1753852460%2C%22av%22%3A1%2C%22fsa%22%3A1753852460%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753852460%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753852472%2C%22ls%22%3A1753852511%2C%22wcv%22%3A1753852511%2C%22wc%22%3A1753852511%7D%2C%222846223%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753852511%2C%22lavid%22%3A1753852458754807%2C%22la%22%3A1753852511%2C%22av%22%3A1%2C%22fsa%22%3A1753852511%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753852462%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=8627480959496981526&cts=1753852525701&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-50-under%2FPageindex%2F6%3Fid%3D305357&request_token=e0864cc6d8e9b0809c557f057c6bc87e9d19d705e213762c70d5e71349d961b2&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T05%3A14%3A14.820Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753852512651%7D&_=1753852457829
[2025/07/30 13:15:26] [MainThread] [Debug] jQuery3710769422935858829_1753852457827()
[2025/07/30 13:15:28] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 13:15:29] [MainThread] [Notice] _exit = 5
[2025/07/30 13:15:29] [MainThread] [Debug] https://www.macys.com/shop/product/wusthof-10-piece-stainless-steak-knife-carving-set-in-olivewood-chest?ID=19740226
[2025/07/30 13:15:32] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 13:15:33] [MainThread] [Debug] 开始安全点击元素: button:has-text("Add To Bag")
[2025/07/30 13:15:33] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 13:15:33] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Add To Bag")
[2025/07/30 13:15:33] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 13:15:41] [MainThread] [Debug] 鼠标移动到: (996, 382)
[2025/07/30 13:15:42] [MainThread] [Debug] 鼠标点击: (996, 382)
[2025/07/30 13:15:42] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Add To Bag")
[2025/07/30 13:15:42] [MainThread] [Notice] clicked Add To Bag: button:has-text("Add To Bag")
[2025/07/30 13:15:53] [MainThread] [Notice] 点击了Add To Bag按钮，但未监听到返回: Timeout 20000ms exceeded while waiting for event "response"
=========================== logs ===========================
waiting for response https://www.macys.com/xapi/bag/v1/add
============================================================
[2025/07/30 13:15:53] [MainThread] [Notice] 已点击Add To Bag按钮，结束add_cart函数
[2025/07/30 13:15:53] [MainThread] [Debug] 当前购物车数量: 0
[2025/07/30 13:15:53] [MainThread] [Notice] COUNT = 0
[2025/07/30 13:15:53] [MainThread] [Notice] cleanup.
[2025/07/30 13:15:53] [MainThread] [Notice] exit.
[2025/07/30 13:42:18] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 13:42:18] [MainThread] [Debug] 手机号码获取成功: 5204091382
[2025/07/30 13:42:18] [MainThread] [Success] 代理检测通过: 127.0.0.1:50158, 0.472905s
[2025/07/30 13:42:19] [MainThread] [Debug] GhostCursor已禁用，将使用原生点击
[2025/07/30 13:42:19] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 13:42:19] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 13:42:22] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 13:42:22] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 13:42:27] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 13:42:32] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQq+LLizwmTaZM66mnYM5pi4JGT4yvjEntrxAGzuytraKWSUbBzAdsB6TpTYwACOAK6IETwplAxC0PSO8NhI0ipunt7Krn3uzg1N9EwYNlAmIgi4HRzulGHQEbiYlEgAFtCQmAD6bJBIAJ677SKQTPm7mPZMhkLAUQBmTEJl6wBeIrgAtMSEumwIkqkBWBkMlEu9FwlTKkCAA
[2025/07/30 13:42:32] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 13:42:32] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 13:42:34] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:42:34] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:42:34] [MainThread] [Error] local variable 'click_success' referenced before assignment
[2025/07/30 13:42:34] [MainThread] [Notice] COUNT = 0
[2025/07/30 13:42:35] [MainThread] [Notice] cleanup.
[2025/07/30 13:42:35] [MainThread] [Notice] exit.
[2025/07/30 13:45:59] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 13:45:59] [MainThread] [Debug] 手机号码获取成功: 4805299581
[2025/07/30 13:46:00] [MainThread] [Error] 代理检测未通过: 127.0.0.1:50180
[2025/07/30 13:46:05] [MainThread] [Error] 代理检测未通过: 127.0.0.1:50293
[2025/07/30 13:46:10] [MainThread] [Error] 代理检测未通过: 127.0.0.1:50087
[2025/07/30 13:46:11] [MainThread] [Success] 代理检测通过: 127.0.0.1:50140, 1.128431s
[2025/07/30 13:46:11] [MainThread] [Debug] GhostCursor已禁用，将使用原生点击
[2025/07/30 13:46:11] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 13:46:11] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-25-under?id=305359&edge=hybrid
[2025/07/30 13:46:16] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 13:46:16] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 13:46:22] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 13:46:26] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQq+KfyhAGz+zviUmnYM5pi47v5kypbKXs6EZF4xPoSUbBzAdsB6TpTYwACOAK6IEU66DELQ9I7w2EjSKm6e3p7+xP4htfXITBg2UCYiCLgtHO6hudARuJiUSAAW0JCYAPpskEgAnpvNIpBMOZuY9kyGQsBRAGZMQsXLAF4iuAC0xIS62cCb+wguGYZg4xz2RREZUgCwMhkop3ouDKxUgQA
[2025/07/30 13:46:27] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 13:46:27] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 13:46:29] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:46:29] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:46:29] [MainThread] [Error] local variable 'click_success' referenced before assignment
[2025/07/30 13:46:29] [MainThread] [Notice] COUNT = 0
[2025/07/30 13:46:29] [MainThread] [Notice] cleanup.
[2025/07/30 13:46:29] [MainThread] [Notice] exit.
[2025/07/30 13:47:42] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 13:47:42] [MainThread] [Debug] 手机号码获取成功: 6023239467
[2025/07/30 13:47:44] [MainThread] [Error] 代理检测未通过: 127.0.0.1:50331
[2025/07/30 13:47:44] [MainThread] [Success] 代理检测通过: 127.0.0.1:50131, 0.311608s
[2025/07/30 13:47:45] [MainThread] [Debug] GhostCursor启用快速模式
[2025/07/30 13:47:45] [MainThread] [Debug] GhostCursor已启用，快速模式: 启用
[2025/07/30 13:47:45] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 13:47:45] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 13:47:48] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 13:47:48] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 13:47:53] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 13:47:56] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQq+LFn+dwBs2oFklJp2DOaYuM4urtreQe7OAc4+AcrK7pRsHMB2wHpOlNjAAI4AroiRPAGUDELQ9I7w2EjSKm6e3r6uhG71jfRMGDZQJiIIuO0c2eHQkbiYlEgAFtCQmAD6bJBIAJ5bbSKQTHlbmPZMhkLA0QBmTEKlKwBeIrgAtMSEutgiFUgiwMhkoZ3ouAqpUgQA
[2025/07/30 13:47:56] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 13:47:58] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:47:58] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:47:58] [MainThread] [Debug] 开始安全点击元素: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:47:58] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 13:47:58] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Click to Claim 25% Off")
[2025/07/30 13:47:58] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 13:48:06] [MainThread] [Debug] 鼠标移动到: (641, 438)
[2025/07/30 13:48:08] [MainThread] [Debug] 鼠标点击: (641, 438)
[2025/07/30 13:48:08] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:48:08] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 13:48:11] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 13:48:40] [MainThread] [Debug] 输入文本: <EMAIL>
[2025/07/30 13:48:40] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 13:48:42] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 13:48:44] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37107111415080005833_1753854471332&campaign_id=2842942&date_formatted=7%2F29%2F2025%2022%3A48%3A42.837&email=bradley1970anyhhh%40emailmacys.store&carb-trap=&email=bradley1970anyhhh%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753854472354&useragent:devicetype=desktop&step=2&visit_id=1753854472360369&m=0&d=d&cookie=%7B%22did%22%3A%227175054603767446113%22%2C%22vid%22%3A1753854472360369%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753854472354%7D%2C%22fvt%22%3A1753854472%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fmacys-deals-and-coupons%253Fid%253D334356%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753854472%2C%22sid%22%3A11%2C%22gcr%22%3A94%2C%22m%22%3A0%2C%22lvt%22%3A1753854476%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753854476%2C%22lavid%22%3A1753854472360369%2C%22la%22%3A1753854474%2C%22av%22%3A1%2C%22fsa%22%3A1753854474%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753854474%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753854486%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753854476%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753854476%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=7175054603767446113&cts=1753854522841&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fmacys-deals-and-coupons%3Fid%3D334356&request_token=68dbc0600c57e00ec9f186c3a4adbb7636fe4a0953fd0e8ce1067bdd863342d2&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=false&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T05%3A47%3A48.393Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753854475816%2C%22eift%22%3A1753854489482%2C%22eifet%22%3A3%2C%22eibt%22%3A1753854520771%2C%22eibet%22%3A3%7D&_=1753854471333
[2025/07/30 13:48:44] [MainThread] [Debug] jQuery37107111415080005833_1753854471332()
[2025/07/30 13:48:46] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 13:48:57] [MainThread] [Debug] 输入文本: 6023233147
[2025/07/30 13:48:57] [MainThread] [Notice] 手机号码已填写: 6023233147
[2025/07/30 13:48:59] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 13:49:01] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37107111415080005833_1753854471332&campaign_id=2846223&date_formatted=7%2F29%2F2025%2022%3A49%3A00.334&phone_number=6023235100&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753854523475&useragent:devicetype=desktop&step=1&visit_id=1753854472360369&m=0&d=d&cookie=%7B%22did%22%3A%227175054603767446113%22%2C%22vid%22%3A1753854472360369%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753854523475%7D%2C%22fvt%22%3A1753854472%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fmacys-deals-and-coupons%253Fid%253D334356%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753854472%2C%22sid%22%3A14%2C%22gcr%22%3A94%2C%22m%22%3A0%2C%22lvt%22%3A1753854523%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753854476%2C%22lavid%22%3A1753854472360369%2C%22la%22%3A1753854474%2C%22av%22%3A1%2C%22fsa%22%3A1753854474%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753854474%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753854486%2C%22ls%22%3A1753854523%2C%22wcv%22%3A1753854523%2C%22wc%22%3A1753854523%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753854523%2C%22lavid%22%3A1753854472360369%2C%22la%22%3A1753854523%2C%22av%22%3A1%2C%22fsa%22%3A1753854523%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753854476%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=7175054603767446113&cts=1753854540335&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fmacys-deals-and-coupons%3Fid%3D334356&request_token=68dbc0600c57e00ec9f186c3a4adbb7636fe4a0953fd0e8ce1067bdd863342d2&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=false&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T05%3A47%3A48.393Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753854524908%7D&_=1753854471334
[2025/07/30 13:49:01] [MainThread] [Debug] jQuery37107111415080005833_1753854471332()
[2025/07/30 13:49:03] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 13:49:04] [MainThread] [Notice] _exit = 5
[2025/07/30 13:49:04] [MainThread] [Debug] https://www.macys.com/shop/product/joseph-joseph-2-pc.-slice-sharpen-knife-set?ID=4766733
[2025/07/30 13:49:07] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 13:49:07] [MainThread] [Debug] 开始安全点击元素: button:has-text("Add To Bag")
[2025/07/30 13:49:07] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 13:49:07] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Add To Bag")
[2025/07/30 13:49:07] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 13:49:16] [MainThread] [Debug] 鼠标移动到: (998, 439)
[2025/07/30 13:49:17] [MainThread] [Debug] 鼠标点击: (998, 439)
[2025/07/30 13:49:17] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Add To Bag")
[2025/07/30 13:49:17] [MainThread] [Notice] clicked Add To Bag: button:has-text("Add To Bag")
[2025/07/30 13:49:27] [MainThread] [Notice] 点击了Add To Bag按钮，但未监听到返回: Timeout 20000ms exceeded while waiting for event "response"
=========================== logs ===========================
waiting for response https://www.macys.com/xapi/bag/v1/add
============================================================
[2025/07/30 13:49:27] [MainThread] [Notice] 已点击Add To Bag按钮，结束add_cart函数
[2025/07/30 13:49:27] [MainThread] [Debug] 当前购物车数量: 0
[2025/07/30 13:49:27] [MainThread] [Notice] COUNT = 0
[2025/07/30 13:49:27] [MainThread] [Notice] cleanup.
[2025/07/30 13:49:27] [MainThread] [Notice] exit.
[2025/07/30 21:56:11] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 21:56:11] [MainThread] [Debug] 手机号码获取成功: 5203878336
[2025/07/30 21:56:12] [MainThread] [Success] 代理检测通过: *************:50013, 1.219245s
[2025/07/30 21:56:13] [MainThread] [Debug] GhostCursor启用快速模式
[2025/07/30 21:56:13] [MainThread] [Debug] GhostCursor已启用，快速模式: 启用
[2025/07/30 21:56:13] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 21:56:13] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-10-under?id=305358
[2025/07/30 21:56:18] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 21:56:18] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 21:56:23] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 21:56:28] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 21:56:30] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQoXfPCr14T4AfiUmnYM5pgqxNqu2s4AbJ6uzs6kxMraxK6UbBzAdsB6TpTYwACOAK6IEU66DELQ9I7w2EjSKm6e3r5kmaoNTchMGDZQJiIIuO0c7qEF0BG4mJRIABbQkJgA+myQSACe220ikEz525j2TIZCwFEAZkxCZasAXiK4ALTEhLp5wG2hwguGYZg4pwOpRElUgSwMhko53ouEqZUgQA
[2025/07/30 21:56:30] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 21:56:32] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:56:32] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:56:32] [MainThread] [Debug] 开始安全点击元素: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:56:32] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 21:56:32] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Click to Claim 25% Off")
[2025/07/30 21:56:32] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 21:56:40] [MainThread] [Debug] 鼠标移动到: (641, 441)
[2025/07/30 21:56:42] [MainThread] [Debug] 鼠标点击: (641, 441)
[2025/07/30 21:56:42] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:56:42] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:56:45] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 21:57:11] [MainThread] [Debug] 输入文本: <EMAIL>
[2025/07/30 21:57:11] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 21:57:13] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 21:57:15] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37105119192620441407_1753883784343&campaign_id=2842942&date_formatted=7%2F30%2F2025%2006%3A57%3A13.758&email=martha1946ohmpw6%40emailmacys.store&carb-trap=&email=martha1946ohmpw6%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753883785576&useragent:devicetype=desktop&step=2&visit_id=1753883785582582&m=0&d=d&cookie=%7B%22did%22%3A%221405076385774241045%22%2C%22vid%22%3A1753883785582582%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753883785576%7D%2C%22fvt%22%3A1753883785%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-10-under%253Fid%253D305358%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753883785%2C%22sid%22%3A15%2C%22gcr%22%3A30%2C%22m%22%3A0%2C%22lvt%22%3A1753883790%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753883790%2C%22lavid%22%3A1753883785582582%2C%22la%22%3A1753883788%2C%22av%22%3A1%2C%22fsa%22%3A1753883788%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753883788%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753883800%7D%2C%222846223%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753883790%7D%2C%222846225%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753883790%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=1405076385774241045&cts=1753883833761&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-10-under%3Fid%3D305358&request_token=840596e9885a788c107c9f679527b065b16e1eaf6eb7590fa9a397376c0fedb9&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T13%3A56%3A18.490Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753883789018%2C%22eift%22%3A1753883803196%2C%22eifet%22%3A3%2C%22eibt%22%3A1753883831677%2C%22eibet%22%3A3%7D&_=1753883784344
[2025/07/30 21:57:15] [MainThread] [Debug] jQuery37105119192620441407_1753883784343()
[2025/07/30 21:57:17] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 21:57:26] [MainThread] [Debug] 输入文本: 5203872687
[2025/07/30 21:57:26] [MainThread] [Notice] 手机号码已填写: 5203872687
[2025/07/30 21:57:28] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 21:57:31] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37105119192620441407_1753883784343&campaign_id=2846223&date_formatted=7%2F30%2F2025%2006%3A57%3A29.188&phone_number=5203879097&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753883834607&useragent:devicetype=desktop&step=1&visit_id=1753883785582582&m=0&d=d&cookie=%7B%22did%22%3A%221405076385774241045%22%2C%22vid%22%3A1753883785582582%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753883834607%7D%2C%22fvt%22%3A1753883785%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-10-under%253Fid%253D305358%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753883785%2C%22sid%22%3A18%2C%22gcr%22%3A30%2C%22m%22%3A0%2C%22lvt%22%3A1753883834%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753883790%2C%22lavid%22%3A1753883785582582%2C%22la%22%3A1753883788%2C%22av%22%3A1%2C%22fsa%22%3A1753883788%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753883788%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753883800%2C%22ls%22%3A1753883834%2C%22wcv%22%3A1753883834%2C%22wc%22%3A1753883834%7D%2C%222846223%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753883834%2C%22lavid%22%3A1753883785582582%2C%22la%22%3A1753883834%2C%22av%22%3A1%2C%22fsa%22%3A1753883834%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A3%2C%22lvt%22%3A1753883790%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=1405076385774241045&cts=1753883849188&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-10-under%3Fid%3D305358&request_token=840596e9885a788c107c9f679527b065b16e1eaf6eb7590fa9a397376c0fedb9&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T13%3A56%3A18.490Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753883835564%7D&_=1753883784345
[2025/07/30 21:57:31] [MainThread] [Debug] jQuery37105119192620441407_1753883784343()
[2025/07/30 21:57:33] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 21:57:34] [MainThread] [Notice] _exit = 5
[2025/07/30 21:57:34] [MainThread] [Debug] https://www.macys.com/shop/product/zwilling-j.a.-henckels-12-slot-in-drawer-knife-organizer?ID=8999983
[2025/07/30 21:57:37] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 21:57:37] [MainThread] [Debug] 开始安全点击元素: button:has-text("Add To Bag")
[2025/07/30 21:57:37] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 21:57:37] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Add To Bag")
[2025/07/30 21:57:37] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 21:57:46] [MainThread] [Debug] 鼠标移动到: (994, 437)
[2025/07/30 21:57:47] [MainThread] [Debug] 鼠标点击: (994, 437)
[2025/07/30 21:57:47] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Add To Bag")
[2025/07/30 21:57:47] [MainThread] [Notice] clicked Add To Bag: button:has-text("Add To Bag")
[2025/07/30 21:57:57] [MainThread] [Notice] 点击了Add To Bag按钮，但未监听到返回: Timeout 20000ms exceeded while waiting for event "response"
=========================== logs ===========================
waiting for response https://www.macys.com/xapi/bag/v1/add
============================================================
[2025/07/30 21:57:57] [MainThread] [Notice] 已点击Add To Bag按钮，结束add_cart函数
[2025/07/30 21:57:57] [MainThread] [Debug] 当前购物车数量: 0
[2025/07/30 21:57:57] [MainThread] [Notice] COUNT = 0
[2025/07/30 21:57:58] [MainThread] [Notice] cleanup.
[2025/07/30 21:57:58] [MainThread] [Notice] exit.
[2025/07/30 21:58:13] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 21:58:13] [MainThread] [Debug] 手机号码获取成功: 7402399780
[2025/07/30 21:58:13] [MainThread] [Success] 代理检测通过: *************:50015, 0.738929s
[2025/07/30 21:58:14] [MainThread] [Debug] GhostCursor启用快速模式
[2025/07/30 21:58:14] [MainThread] [Debug] GhostCursor已启用，快速模式: 启用
[2025/07/30 21:58:14] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 21:58:14] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-25-under?id=305359&edge=hybrid
[2025/07/30 21:58:19] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 21:58:19] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 21:58:24] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 21:58:29] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 21:58:30] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQoXdltANjIvZWDKTTsGc0wCd1diQj9-QkD3d1JnYgTKNg5gO2A9J0psYABHAFdECJ5nSgYhaHpHeGwkaRU3T28yZW041TqG5CYMGygTEQRcVo53UNzoCNxMSiQAC2hITAB9NkgkAE8tlpFIJhytzHsmQyFgKIAzJiFilYAvEVwAWjjdbOAtg4QXDMMwcE77IoiMqQRYGQyUM70XBlYqQIA
[2025/07/30 21:58:31] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 21:58:33] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:58:33] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:58:33] [MainThread] [Debug] 开始安全点击元素: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:58:33] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 21:58:33] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Click to Claim 25% Off")
[2025/07/30 21:58:33] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 21:58:41] [MainThread] [Debug] 鼠标移动到: (639, 440)
[2025/07/30 21:58:43] [MainThread] [Debug] 鼠标点击: (639, 440)
[2025/07/30 21:58:43] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:58:43] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 21:58:46] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 21:59:12] [MainThread] [Debug] 输入文本: <EMAIL>
[2025/07/30 21:59:12] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 21:59:14] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 21:59:19] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery371049563267476447326_1753883905489&campaign_id=2842942&date_formatted=7%2F30%2F2025%2006%3A59%3A14.474&email=shane1947g2f7cm%40emailmacys.store&carb-trap=&email=shane1947g2f7cm%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753883906982&useragent:devicetype=desktop&step=2&visit_id=1753883906988111&m=0&d=d&cookie=%7B%22did%22%3A%222354860689833427460%22%2C%22vid%22%3A1753883906988111%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753883906982%7D%2C%22fvt%22%3A1753883906%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-25-under%253Fid%253D305359%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753883906%2C%22sid%22%3A12%2C%22gcr%22%3A75%2C%22m%22%3A0%2C%22lvt%22%3A1753883910%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753883910%2C%22lavid%22%3A1753883906988111%2C%22la%22%3A1753883908%2C%22av%22%3A1%2C%22fsa%22%3A1753883908%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753883908%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753883920%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753883910%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753883910%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=2354860689833427460&cts=1753883954477&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-25-under%3Fid%3D305359%26edge%3Dhybrid&request_token=886d272d254dd9f69520f872233191f56dc0faa29475fc88a33be7cb539ef741&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T13%3A58%3A20.391Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753883910411%2C%22eift%22%3A1753883924205%2C%22eifet%22%3A3%2C%22eibt%22%3A1753883952415%2C%22eibet%22%3A3%7D&_=1753883905490
[2025/07/30 21:59:19] [MainThread] [Debug] jQuery371049563267476447326_1753883905489()
[2025/07/30 21:59:21] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 21:59:30] [MainThread] [Debug] 输入文本: 7402394073
[2025/07/30 21:59:30] [MainThread] [Notice] 手机号码已填写: 7402394073
[2025/07/30 21:59:32] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 21:59:34] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery371049563267476447326_1753883905489&campaign_id=2846223&date_formatted=7%2F30%2F2025%2006%3A59%3A33.392&phone_number=7402398887&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753883955356&useragent:devicetype=desktop&step=1&visit_id=1753883906988111&m=0&d=d&cookie=%7B%22did%22%3A%222354860689833427460%22%2C%22vid%22%3A1753883906988111%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753883955356%7D%2C%22fvt%22%3A1753883906%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-25-under%253Fid%253D305359%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753883906%2C%22sid%22%3A15%2C%22gcr%22%3A75%2C%22m%22%3A0%2C%22lvt%22%3A1753883957%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753883910%2C%22lavid%22%3A1753883906988111%2C%22la%22%3A1753883908%2C%22av%22%3A1%2C%22fsa%22%3A1753883908%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753883908%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753883920%2C%22ls%22%3A1753883957%2C%22wcv%22%3A1753883957%2C%22wc%22%3A1753883957%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753883957%2C%22lavid%22%3A1753883906988111%2C%22la%22%3A1753883957%2C%22av%22%3A1%2C%22fsa%22%3A1753883957%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753883910%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=2354860689833427460&cts=1753883973393&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-25-under%3Fid%3D305359%26edge%3Dhybrid&request_token=886d272d254dd9f69520f872233191f56dc0faa29475fc88a33be7cb539ef741&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T13%3A58%3A20.391Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753883959261%7D&_=1753883905491
[2025/07/30 21:59:34] [MainThread] [Debug] jQuery371049563267476447326_1753883905489()
[2025/07/30 21:59:36] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 21:59:37] [MainThread] [Notice] _exit = 5
[2025/07/30 21:59:37] [MainThread] [Debug] https://www.macys.com/shop/product/zwilling-j.a.-henckels-gourmet-8-carving-slicing-knife?ID=8999996
[2025/07/30 21:59:40] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 21:59:40] [MainThread] [Debug] 开始安全点击元素: button:has-text("Add To Bag")
[2025/07/30 21:59:40] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 21:59:40] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Add To Bag")
[2025/07/30 21:59:40] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 21:59:49] [MainThread] [Debug] 鼠标移动到: (996, 440)
[2025/07/30 21:59:50] [MainThread] [Debug] 鼠标点击: (996, 440)
[2025/07/30 21:59:50] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Add To Bag")
[2025/07/30 21:59:50] [MainThread] [Notice] clicked Add To Bag: button:has-text("Add To Bag")
[2025/07/30 21:59:52] [MainThread] [Success] https://www.macys.com/xapi/bag/v1/add, {"item":{"upcId":"41165388","upcNumber":"35886389963","registryId":0,"source":"PDPA2B","loyaltyPoints":true,"quantity":1,"pickUpFromStore":"false","pdpAtbProtectionPlanEligible":false,"isMarketPlaceEligibleFlag":false,"sellerId":null,"autoreplenishment":null}}
[2025/07/30 21:59:52] [MainThread] [Notice] 点击了Add To Bag按钮，但未监听到返回: Response.json: Response body is unavailable for redirect responses
[2025/07/30 21:59:52] [MainThread] [Notice] 已点击Add To Bag按钮，结束add_cart函数
[2025/07/30 21:59:52] [MainThread] [Debug] 当前购物车数量: 0
[2025/07/30 21:59:52] [MainThread] [Notice] COUNT = 0
[2025/07/30 21:59:53] [MainThread] [Notice] cleanup.
[2025/07/30 21:59:53] [MainThread] [Notice] exit.
[2025/07/30 22:09:49] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 22:09:49] [MainThread] [Debug] 手机号码获取成功: 7027066512
[2025/07/30 22:09:54] [MainThread] [Error] 代理检测未通过: *************:50045
[2025/07/30 22:09:55] [MainThread] [Success] 代理检测通过: *************:50020, 1.228236s
[2025/07/30 22:09:55] [MainThread] [Debug] GhostCursor已禁用，将使用原生点击
[2025/07/30 22:09:55] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 22:09:55] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-50-under/Pageindex/6?id=305357
[2025/07/30 22:10:04] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 22:10:04] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 22:10:09] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 22:10:14] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 22:10:19] [MainThread] [Debug] 已等待 15 秒，继续等待弹窗信号...
[2025/07/30 22:10:24] [MainThread] [Debug] 已等待 20 秒，继续等待弹窗信号...
[2025/07/30 22:10:29] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQiQBs+cq8JXbXdKTTsGc0wVZXwY12JnbUJ+AO9nF0o2DmA7YD0nSmxgAEcAV0QInmdKBiFoekd4bCRpFTdPH3J3V28auvomDBsoExEEXBaOELDoCNxMSiQAC2hITAB9NkgkAE915pFIJmz1zHsmQyFgKIAzJiEixYAvEVwAWmJCXSzgdd2IXDMMwcQ47QoiUqQOYGQyUY70XClIqQIA
[2025/07/30 22:10:29] [MainThread] [Debug] 已等待 25 秒，继续等待弹窗信号...
[2025/07/30 22:10:29] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 22:10:31] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:10:31] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:10:31] [MainThread] [Error] local variable 'click_success' referenced before assignment
[2025/07/30 22:10:31] [MainThread] [Notice] COUNT = 0
[2025/07/30 22:10:31] [MainThread] [Notice] cleanup.
[2025/07/30 22:10:31] [MainThread] [Notice] exit.
[2025/07/30 22:11:08] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 22:11:08] [MainThread] [Debug] 手机号码获取成功: 6029333790
[2025/07/30 22:11:12] [MainThread] [Success] 代理检测通过: *************:50030, 3.275273s
[2025/07/30 22:11:12] [MainThread] [Debug] GhostCursor启用快速模式
[2025/07/30 22:11:12] [MainThread] [Debug] GhostCursor已启用，快速模式: 启用
[2025/07/30 22:11:12] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 22:11:12] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-10-under?id=305358&edge=hybrid
[2025/07/30 22:11:17] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 22:11:17] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 22:11:23] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 22:11:28] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 22:11:33] [MainThread] [Debug] 已等待 15 秒，继续等待弹窗信号...
[2025/07/30 22:11:38] [MainThread] [Debug] 已等待 20 秒，继续等待弹窗信号...
[2025/07/30 22:11:43] [MainThread] [Debug] 已等待 25 秒，继续等待弹窗信号...
[2025/07/30 22:11:48] [MainThread] [Debug] 已等待 30 秒，继续等待弹窗信号...
[2025/07/30 22:11:53] [MainThread] [Debug] 已等待 35 秒，继续等待弹窗信号...
[2025/07/30 22:11:58] [MainThread] [Debug] 已等待 40 秒，继续等待弹窗信号...
[2025/07/30 22:12:01] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQiWfKAbGXxlfHdnSk07BnNMXEIyN2JXZ3wPZN9tYgyyX0o2DmA7YD0nSmxgAEcAV0RInlCGIWh6R3hsJGkVN09vQPdfQkp6xuQmDBsoExEEXDaOdzD86EjcTEokAAtoSEwAfTZIJABPbdaRSCY87cx7JkMhYGiAMyYhUtWALxFcAFpiQl1c4DbQ4QXDMMwcU4HEoiCqQJYGQyUc70XAVUqQIA
[2025/07/30 22:12:02] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 22:12:04] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:12:04] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:12:04] [MainThread] [Debug] 开始安全点击元素: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:12:04] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 22:12:04] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Click to Claim 25% Off")
[2025/07/30 22:12:04] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 22:12:12] [MainThread] [Debug] 鼠标移动到: (641, 437)
[2025/07/30 22:12:14] [MainThread] [Debug] 鼠标点击: (641, 437)
[2025/07/30 22:12:14] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:12:14] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:12:17] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 22:12:46] [MainThread] [Debug] 输入文本: <EMAIL>
[2025/07/30 22:12:46] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 22:12:48] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 22:12:58] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery371014701722695920527_1753884715373&campaign_id=2842942&date_formatted=7%2F30%2F2025%2007%3A12%3A48.404&email=dylan20055ves79%40emailmacys.store&carb-trap=&email=dylan20055ves79%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753884716914&useragent:devicetype=desktop&step=2&visit_id=1753884716921237&m=0&d=d&cookie=%7B%22did%22%3A%228975457253256044496%22%2C%22vid%22%3A1753884716921237%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753884716914%7D%2C%22fvt%22%3A1753884716%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-10-under%253Fid%253D305358%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753884716%2C%22sid%22%3A12%2C%22gcr%22%3A30%2C%22m%22%3A0%2C%22lvt%22%3A1753884719%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753884719%2C%22lavid%22%3A1753884716921237%2C%22la%22%3A1753884718%2C%22av%22%3A1%2C%22fsa%22%3A1753884718%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753884718%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753884730%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753884719%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753884719%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=8975457253256044496&cts=1753884768404&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-10-under%3Fid%3D305358%26edge%3Dhybrid&request_token=2bf4cde6fbecece2275d7fc088ea63f15cab4f52b162812b46c9a42edcfe6372&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T14%3A11%3A18.294Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753884721288%2C%22eift%22%3A1753884735241%2C%22eifet%22%3A3%2C%22eibt%22%3A1753884766339%2C%22eibet%22%3A3%7D&_=1753884715374
[2025/07/30 22:12:58] [MainThread] [Debug] jQuery371014701722695920527_1753884715373()
[2025/07/30 22:13:00] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 22:13:09] [MainThread] [Debug] 输入文本: 6029332374
[2025/07/30 22:13:09] [MainThread] [Notice] 手机号码已填写: 6029332374
[2025/07/30 22:13:11] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 22:13:13] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery371014701722695920527_1753884715373&campaign_id=2846223&date_formatted=7%2F30%2F2025%2007%3A13%3A11.825&phone_number=6029337086&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753884770313&useragent:devicetype=desktop&step=1&visit_id=1753884716921237&m=0&d=d&cookie=%7B%22did%22%3A%228975457253256044496%22%2C%22vid%22%3A1753884716921237%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753884770313%7D%2C%22fvt%22%3A1753884716%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252Fsale-10-under%253Fid%253D305358%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753884716%2C%22sid%22%3A15%2C%22gcr%22%3A30%2C%22m%22%3A0%2C%22lvt%22%3A1753884775%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753884719%2C%22lavid%22%3A1753884716921237%2C%22la%22%3A1753884718%2C%22av%22%3A1%2C%22fsa%22%3A1753884718%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753884718%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753884730%2C%22ls%22%3A1753884775%2C%22wcv%22%3A1753884775%2C%22wc%22%3A1753884775%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753884775%2C%22lavid%22%3A1753884716921237%2C%22la%22%3A1753884775%2C%22av%22%3A1%2C%22fsa%22%3A1753884775%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753884719%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=8975457253256044496&cts=1753884791826&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fsale-10-under%3Fid%3D305358%26edge%3Dhybrid&request_token=2bf4cde6fbecece2275d7fc088ea63f15cab4f52b162812b46c9a42edcfe6372&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T14%3A11%3A18.294Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753884778215%7D&_=1753884715375
[2025/07/30 22:13:13] [MainThread] [Debug] jQuery371014701722695920527_1753884715373()
[2025/07/30 22:13:15] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 22:13:16] [MainThread] [Notice] _exit = 5
[2025/07/30 22:13:16] [MainThread] [Debug] https://www.macys.com/shop/product/shun-classic-6.5-nakiri-knife?ID=9012495
[2025/07/30 22:13:19] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 22:13:20] [MainThread] [Notice] 最终降级点击Add To Bag: button:has-text("Add To Bag")
[2025/07/30 22:13:25] [MainThread] [Success] https://www.macys.com/xapi/bag/v1/add, {"item":{"upcId":"41213218","upcNumber":"87171056180","registryId":0,"source":"PDPA2B","loyaltyPoints":true,"quantity":1,"pickUpFromStore":"false","pdpAtbProtectionPlanEligible":false,"isMarketPlaceEligibleFlag":false,"sellerId":null,"autoreplenishment":null}}
[2025/07/30 22:13:25] [MainThread] [Notice] 点击了Add To Bag按钮，但未监听到返回: Response.json: Response body is unavailable for redirect responses
[2025/07/30 22:13:25] [MainThread] [Notice] 已点击Add To Bag按钮，结束add_cart函数
[2025/07/30 22:13:25] [MainThread] [Debug] 当前购物车数量: 0
[2025/07/30 22:13:25] [MainThread] [Notice] COUNT = 0
[2025/07/30 22:13:26] [MainThread] [Notice] cleanup.
[2025/07/30 22:13:26] [MainThread] [Notice] exit.
[2025/07/30 22:15:26] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 22:15:26] [MainThread] [Debug] 手机号码获取成功: 7193980872
[2025/07/30 22:15:28] [MainThread] [Success] 代理检测通过: *************:50045, 1.600903s
[2025/07/30 22:15:28] [MainThread] [Debug] GhostCursor启用快速模式
[2025/07/30 22:15:28] [MainThread] [Debug] GhostCursor已启用，快速模式: 启用
[2025/07/30 22:15:28] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 22:15:28] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/100-under-sale?id=163454&edge=hybrid
[2025/07/30 22:15:35] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 22:15:35] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 22:15:40] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 22:15:45] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 22:15:50] [MainThread] [Debug] 已等待 15 秒，继续等待弹窗信号...
[2025/07/30 22:15:55] [MainThread] [Debug] 已等待 20 秒，继续等待弹窗信号...
[2025/07/30 22:16:00] [MainThread] [Debug] 已等待 25 秒，继续等待弹窗信号...
[2025/07/30 22:16:05] [MainThread] [Debug] 已等待 30 秒，继续等待弹窗信号...
[2025/07/30 22:16:10] [MainThread] [Debug] 已等待 35 秒，继续等待弹窗信号...
[2025/07/30 22:16:14] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQiQpk3hfO4AbJSadgzmmATOxM6EzuTKZC740cSWxJRsHMB2wHpOlNjAAI4ArojhPM6UDELQ9I7w2EjSKm6e3qlBNXX0TBg2UCYiCLgtHO4hOdDhuJiUSAAW0JCYAPpskEgAnmvNIpBM2WuY9kyGQsCRAGZMQkULAF4iuAC0xIS6WcBrOxC4zDMHAO20KIlKkFmBkMlCO9FwpSKkCAA
[2025/07/30 22:16:15] [MainThread] [Debug] 已等待 40 秒，继续等待弹窗信号...
[2025/07/30 22:16:15] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 22:16:17] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:16:17] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:16:17] [MainThread] [Debug] 开始安全点击元素: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:16:17] [MainThread] [Debug] 找到 1 个匹配的元素
[2025/07/30 22:16:17] [MainThread] [Debug] 尝试点击元素 (第1次): button:has-text("Click to Claim 25% Off")
[2025/07/30 22:16:17] [MainThread] [Debug] 方法1: 使用GhostCursor正常点击
[2025/07/30 22:16:26] [MainThread] [Debug] 鼠标移动到: (638, 441)
[2025/07/30 22:16:27] [MainThread] [Debug] 鼠标点击: (638, 441)
[2025/07/30 22:16:27] [MainThread] [Success] GhostCursor正常点击成功: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:16:27] [MainThread] [Notice] clicked: button:has-text("Click to Claim 25% Off")
[2025/07/30 22:16:30] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2842942-aiDVbjg-input', 'className': 'bx-el bx-input', 'placeholder': 'Your Email Here', 'value': '', 'text': ''}
[2025/07/30 22:16:59] [MainThread] [Debug] 输入文本: <EMAIL>
[2025/07/30 22:16:59] [MainThread] [Notice] 邮箱已填写: <EMAIL>
[2025/07/30 22:17:01] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Get 25% Off'}
[2025/07/30 22:17:11] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37109397839328523129_1753884947251&campaign_id=2842942&date_formatted=7%2F30%2F2025%2007%3A17%3A01.458&email=bradley1948quf1pd%40emailmacys.store&carb-trap=&email=bradley1948quf1pd%40emailmacys.store&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753884949754&useragent:devicetype=desktop&step=2&visit_id=1753884949758236&m=0&d=d&cookie=%7B%22did%22%3A%222747872919172744494%22%2C%22vid%22%3A1753884949758236%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753884949754%7D%2C%22fvt%22%3A1753884949%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252F100-under-sale%253Fid%253D163454%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753884949%2C%22sid%22%3A12%2C%22gcr%22%3A17%2C%22m%22%3A0%2C%22lvt%22%3A1753884952%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753884952%2C%22lavid%22%3A1753884949758236%2C%22la%22%3A1753884951%2C%22av%22%3A1%2C%22fsa%22%3A1753884951%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753884951%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753884963%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753884952%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753884952%7D%7D%7D&pos=overlay&step_name=before&last_step=1&device_id=2747872919172744494&cts=1753885021461&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2F100-under-sale%3Fid%3D163454%26edge%3Dhybrid&request_token=9fdea41a9ecb3903e4d90b8744a1bd18e440ddee5cc791f344ebd7d5f7c05bda&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T14%3A15%3A38.037Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753884974344%2C%22eift%22%3A1753884988554%2C%22eifet%22%3A3%2C%22eibt%22%3A1753885019379%2C%22eibet%22%3A3%7D&_=1753884947252
[2025/07/30 22:17:11] [MainThread] [Debug] jQuery37109397839328523129_1753884947251()
[2025/07/30 22:17:13] [MainThread] [Debug] 焦点元素信息: {'tagName': 'INPUT', 'id': 'bx-element-2846223-WefZKz0-input', 'className': 'bx-el bx-input', 'placeholder': 'Phone Number', 'value': '', 'text': ''}
[2025/07/30 22:17:22] [MainThread] [Debug] 输入文本: 7193988981
[2025/07/30 22:17:22] [MainThread] [Notice] 手机号码已填写: 7193988981
[2025/07/30 22:17:24] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Sign Up for Texts\n'}
[2025/07/30 22:17:27] [MainThread] [Success] status_code=200, url=https://api.bounceexchange.com/capture/submit?callback=jQuery37109397839328523129_1753884947251&campaign_id=2846223&date_formatted=7%2F30%2F2025%2007%3A17%3A24.809&phone_number=7193982612&carb-trap=&dg:isSubscriber=false&dg:isPreviousCustomer=false&dg:cacheTS=1753885022952&useragent:devicetype=desktop&step=1&visit_id=1753884949758236&m=0&d=d&cookie=%7B%22did%22%3A%222747872919172744494%22%2C%22vid%22%3A1753884949758236%2C%22v%22%3A%7B%22ever_logged_in%22%3Afalse%2C%22cart_items%22%3Afalse%2C%22cart%22%3Afalse%2C%22cart_items_qty%22%3Afalse%2C%22cart_items_offset%22%3Afalse%2C%22cart_set%22%3Afalse%2C%22logged_in_identified%22%3Afalse%2C%22welcome_code%22%3Afalse%2C%22starrewards_login_ever%22%3Afalse%2C%22quickreg_submit%22%3Afalse%2C%22quickreg_submit_ever%22%3Afalse%2C%22is_subscribed%22%3Afalse%2C%22coupon_code_url%22%3Afalse%2C%22sfl_item_ids%22%3Afalse%2C%22cart_images%22%3Afalse%2C%22impression_ts%22%3Afalse%7D%2C%22pdFirstLoad%22%3Atrue%2C%22osIdReady%22%3Atrue%2C%22dg%22%3A%7B%22isPreviousCustomer%22%3Afalse%2C%22isSubscriber%22%3Afalse%2C%22isTextSubscriber%22%3Afalse%2C%22cache_ts%22%3A1753885022952%7D%2C%22fvt%22%3A1753884949%2C%22ao%22%3A1%2C%22lp%22%3A%22https%253A%252F%252Fwww.macys.com%252Fshop%252Fsale%252F100-under-sale%253Fid%253D163454%2526edge%253Dhybrid%22%2C%22as%22%3A1%2C%22vpv%22%3A1%2C%22vs%22%3A1%2C%22r%22%3A%22%22%2C%22cvt%22%3A1753884949%2C%22sid%22%3A15%2C%22gcr%22%3A17%2C%22m%22%3A0%2C%22lvt%22%3A1753885008%2C%22d%22%3A%22d%22%2C%22campaigns%22%3A%7B%222842942%22%3A%7B%22vv%22%3A1%2C%22lvt%22%3A1753884952%2C%22lavid%22%3A1753884949758236%2C%22la%22%3A1753884951%2C%22av%22%3A1%2C%22fsa%22%3A1753884951%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B1753884951%5D%2C%22io%22%3A1%2C%22lclk%22%3A1753884963%2C%22ls%22%3A1753885008%2C%22wcv%22%3A1753885008%2C%22wc%22%3A1753885008%7D%2C%222846223%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753885008%2C%22lavid%22%3A1753884949758236%2C%22la%22%3A1753885008%2C%22av%22%3A1%2C%22fsa%22%3A1753885008%2C%22as%22%3A1%2C%22ao%22%3A1%2C%22oa%22%3A%5B%5D%7D%2C%222846225%22%3A%7B%22vv%22%3A2%2C%22lvt%22%3A1753884952%7D%7D%2C%22uid%22%3A1%2C%22es%22%3Atrue%7D&pos=overlay&step_name=before&last_step=1&device_id=2747872919172744494&cts=1753885044810&pvid=1&url=https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2F100-under-sale%3Fid%3D163454%26edge%3Dhybrid&request_token=9fdea41a9ecb3903e4d90b8744a1bd18e440ddee5cc791f344ebd7d5f7c05bda&reloadCampaignsV2=1&language=zh&var[logged_in]=false&var[ever_logged_in]=false&var[cart_qty]=0&var[cart_items]=false&var[cart]=false&var[cookie_modal_present]=false&var[page_type]=category&var[cart_items_qty]=false&var[cart_items_offset]=false&var[cart_set]=false&var[logged_in_identified]=false&var[welcome_code]=false&var[starrewards_login]=false&var[starrewards_login_ever]=false&var[quickreg_submit]=false&var[quickreg_submit_ever]=false&var[is_subscribed]=false&var[coupon_code_url]=false&var[utag_data_event_name]=&var[utag_data_event_timestamp]=2025-07-30T14%3A15%3A38.037Z&var[sfl_item_ids]=false&var[cart_images]=false&var[wknd_overlay_present]=true&var[impression_ts]=false&bed=%7B%22pt%22%3A1753885031332%7D&_=1753884947253
[2025/07/30 22:17:27] [MainThread] [Debug] jQuery37109397839328523129_1753884947251()
[2025/07/30 22:17:29] [MainThread] [Debug] 焦点元素信息: {'tagName': 'BUTTON', 'id': '', 'className': 'bx-button', 'placeholder': None, 'value': '', 'text': 'Continue Shopping'}
[2025/07/30 22:17:35] [MainThread] [Notice] _exit = 5
[2025/07/30 22:17:35] [MainThread] [Debug] https://www.macys.com/shop/product/oxo-kitchen-herb-scissors?ID=400986
[2025/07/30 22:17:40] [MainThread] [Notice] wait for selector: button:has-text("Add To Bag")
[2025/07/30 22:17:41] [MainThread] [Notice] 最终降级点击Add To Bag: button:has-text("Add To Bag")
[2025/07/30 22:17:48] [MainThread] [Success] https://www.macys.com/xapi/bag/v1/add, {"item":{"upcId":"11473577","upcNumber":"719812019611","registryId":0,"source":"PDPA2B","loyaltyPoints":true,"quantity":1,"pickUpFromStore":"false","pdpAtbProtectionPlanEligible":false,"isMarketPlaceEligibleFlag":false,"sellerId":null,"autoreplenishment":null}}
[2025/07/30 22:17:48] [MainThread] [Notice] 点击了Add To Bag按钮，但未监听到返回: Response.json: Response body is unavailable for redirect responses
[2025/07/30 22:17:48] [MainThread] [Notice] 已点击Add To Bag按钮，结束add_cart函数
[2025/07/30 22:17:48] [MainThread] [Debug] 当前购物车数量: 0
[2025/07/30 22:17:48] [MainThread] [Notice] COUNT = 0
[2025/07/30 22:17:48] [MainThread] [Notice] cleanup.
[2025/07/30 22:17:48] [MainThread] [Notice] exit.
[2025/07/30 23:12:30] [MainThread] [Debug] 邮箱获取成功: <EMAIL>
[2025/07/30 23:12:30] [MainThread] [Debug] 手机号码获取成功: 6232014340
[2025/07/30 23:12:31] [MainThread] [Success] 代理检测通过: *************:50023, 0.663225s
[2025/07/30 23:12:32] [MainThread] [Debug] GhostCursor已禁用，将使用原生点击
[2025/07/30 23:12:32] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 23:12:32] [MainThread] [Notice] goto url: https://www.macys.com/shop/sale/sale-10-under?id=305358&edge=hybrid
[2025/07/30 23:12:39] [MainThread] [Notice] wait_until = domcontentloaded Done.
[2025/07/30 23:12:39] [MainThread] [Notice] 等待弹窗出现信号...
[2025/07/30 23:12:44] [MainThread] [Debug] 已等待 5 秒，继续等待弹窗信号...
[2025/07/30 23:12:49] [MainThread] [Debug] 已等待 10 秒，继续等待弹窗信号...
[2025/07/30 23:12:54] [MainThread] [Success] 检测到弹窗出现信号: https://events.bouncex.net/track.gif/experiment?wklz=KYDwDsBOCWC2wDsAuAuUZoBMDOBeATABwAs+AnKQGTpRyKoDGAhrGE9AOYJYEnlWRgAGwD2TTAGEWbTgmwA1fLgCMlWCMzBcABkoB3YACNs0JMB7EyFSgDdoJpD2UB2AKwBmQl-fPt25fjKZO6UmnYM5pi4zs5kzgBshNqW2onEyeTarpRsHMB2wHpOlNjAAI4ArogRPM6UDELQ9I7w2EjSKm6e3s7uwfH1jfRMGDZQJiIIuO0cIWHQEbiYlEgAFtCQmAD6bJBIAJ5bbSKQTHlbmPZMhkLAUQBmTEKlKwBeIrgAtMRJOWfAWwOEFwzDMHBO+xKIgqkEWBkMlH+yFwFVKkCAA
[2025/07/30 23:12:54] [MainThread] [Debug] 已等待 15 秒，继续等待弹窗信号...
[2025/07/30 23:12:54] [MainThread] [Success] 弹窗已出现，开始查找按钮...
[2025/07/30 23:12:56] [MainThread] [Notice] wait for selector: button:has-text("Click to Claim 25% Off")
[2025/07/30 23:12:56] [MainThread] [Notice] 找到按钮: button:has-text("Click to Claim 25% Off")
[2025/07/30 23:12:56] [MainThread] [Error] local variable 'click_success' referenced before assignment
[2025/07/30 23:12:56] [MainThread] [Notice] COUNT = 0
[2025/07/30 23:12:56] [MainThread] [Notice] cleanup.
[2025/07/30 23:12:56] [MainThread] [Notice] exit.
